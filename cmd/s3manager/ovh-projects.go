package s3manager

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

// ovhProjectsCmd represents the ovh-projects command
var ovhProjectsCmd = &cobra.Command{
	Use:   "ovh-projects",
	Short: "List OVH projects",
	Long:  "List OVH projects using the configured OVH API credentials.",
	RunE:  runOvhProjects,
}

func init() {
	rootCmd.AddCommand(ovhProjectsCmd)
}

func runOvhProjects(cmd *cobra.Command, args []string) error {
	// Access the global configuration
	config := GetGlobalConfig()

	provider, err := ovh.NewProvider(config.OvhEndpoint, config.OvhApplicationKey, config.OvhApplicationSecret, config.OvhConsumerKey, config.OvhProjectId)
	if err != nil {
		return fmt.Errorf("failed to create OVH provider: %w", err)
	}

	result, err := provider.ExecApi("get", "/cloud/project", "")
	if err != nil {
		fmt.Printf("API call failed: %v", err)
		return err
	}

	// Pretty print the result
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("%+v\n", result)
	} else {
		fmt.Printf("%s\n", string(resultJSON))
	}

	return nil
}
